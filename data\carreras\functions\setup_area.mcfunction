# Configurar área de selección de carreras
# Este comando debe ser ejecutado por un admin una sola vez

# Crear carteles informativos
setblock ~ ~ ~ oak_sign[rotation=0]{
    Text1:'{"text":"=== CARRERAS ===","color":"gold","bold":true}',
    Text2:'{"text":"Haz click en el","color":"black"}',
    Text3:'{"text":"botón de abajo","color":"black"}',
    Text4:'{"text":"para elegir","color":"black"}'
}

# Crear botón para activar menú
setblock ~ ~-1 ~ stone_button[face=floor]

# Crear bloque de comando que se activa con el botón
setblock ~ ~-2 ~ command_block{
    Command:"function carreras:menu",
    auto:0b
}

tellraw @s [{"text":"✅ Área de carreras configurada","color":"green","bold":true}]
tellraw @s [{"text":"Los jugadores pueden hacer click en el botón para elegir carrera","color":"gray"}]
