# Sistema automático para mostrar menú a jugadores nuevos
# Se ejecuta automáticamente para jugadores sin carrera

# Verificar si el jugador no tiene ninguna carrera
execute as @s unless entity @s[tag=carrera_velocista] unless entity @s[tag=carrera_tanque] unless entity @s[tag=carrera_mago] unless entity @s[tag=carrera_arquero] run function carreras:menu

# Marcar que ya se mostró el menú
tag @s add menu_mostrado
