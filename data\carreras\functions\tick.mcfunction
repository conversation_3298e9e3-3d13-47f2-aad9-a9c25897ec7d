# Función que se ejecuta cada tick
# Detecta automáticamente jugadores nuevos y les da opciones

# Detectar jugadores que acaban de entrar al mundo (sin carrera y sin tag de bienvenida)
execute as @a[tag=!bienvenido] unless entity @s[tag=carrera_velocista] unless entity @s[tag=carrera_tanque] unless entity @s[tag=carrera_mago] unless entity @s[tag=carrera_arquero] run function carreras:bienvenida

# Detectar uso de items (cada 20 ticks para no sobrecargar)
execute if score #tick_counter carreras_timer matches 0 run function carreras:detectar_items

# Contador de ticks
scoreboard players add #tick_counter carreras_timer 1
execute if score #tick_counter carreras_timer matches 20.. run scoreboard players set #tick_counter carreras_timer 0
