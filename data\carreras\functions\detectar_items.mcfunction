# Sistema de detección por items específicos
# Se ejecuta continuamente para detectar cuando un jugador tiene ciertos items

# Detectar si el jugador tiene una brújula (item para abrir menú)
execute as @a[nbt={SelectedItem:{id:"minecraft:compass",tag:{display:{Name:'{"text":"Selector de Carrera","color":"gold","bold":true}'}}}}] run function carreras:menu

# Detectar items específicos para carreras directas
execute as @a[nbt={SelectedItem:{id:"minecraft:feather"}}] run function carreras:velocista
execute as @a[nbt={SelectedItem:{id:"minecraft:iron_ingot"}}] run function carreras:tanque  
execute as @a[nbt={SelectedItem:{id:"minecraft:blaze_powder"}}] run function carreras:mago
execute as @a[nbt={SelectedItem:{id:"minecraft:arrow"}}] run function carreras:arquero

# Limpiar items después de usar
clear @a[nbt={SelectedItem:{id:"minecraft:feather"}}] feather 1
clear @a[nbt={SelectedItem:{id:"minecraft:iron_ingot"}}] iron_ingot 1
clear @a[nbt={SelectedItem:{id:"minecraft:blaze_powder"}}] blaze_powder 1
clear @a[nbt={SelectedItem:{id:"minecraft:arrow"}}] arrow 1
